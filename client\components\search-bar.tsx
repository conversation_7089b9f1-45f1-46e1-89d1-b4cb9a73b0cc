"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { 
  Search, 
  MapPin, 
  Filter, 
  X,
  SlidersHorizontal
} from "lucide-react"
import { cn } from "@/lib/utils"

export interface SearchFilters {
  location: string
  type: "semua" | "putra" | "putri" | "campur"
  priceRange: [number, number]
  facilities: string[]
  sortBy: "relevance" | "price-low" | "price-high" | "rating" | "newest"
}

interface SearchBarProps {
  onSearch: (query: string, filters: SearchFilters) => void
  className?: string
  placeholder?: string
  showFilters?: boolean
}

const availableFacilities = [
  "WiFi",
  "Parkir",
  "Dapur",
  "<PERSON><PERSON>",
  "Air",
  "Keamanan",
  "Ruang Tamu",
  "AC",
  "Ka<PERSON>r",
  "Lemari"
]

const locations = [
  "Semua Lokasi",
  "Jakarta Pusat",
  "Jakarta Selatan", 
  "Jakarta Barat",
  "Jakarta Utara",
  "Jakarta Timur",
  "Bandung",
  "Surabaya",
  "Yogyakarta",
  "Semarang",
  "Malang"
]

export function SearchBar({ 
  onSearch, 
  className,
  placeholder = "Cari kost berdasarkan lokasi, nama, atau fasilitas...",
  showFilters = true
}: SearchBarProps) {
  const [query, setQuery] = useState("")
  const [filters, setFilters] = useState<SearchFilters>({
    location: "",
    type: "semua",
    priceRange: [500000, 5000000],
    facilities: [],
    sortBy: "relevance"
  })
  const [isFilterOpen, setIsFilterOpen] = useState(false)

  const handleSearch = () => {
    onSearch(query, filters)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const toggleFacility = (facility: string) => {
    setFilters(prev => ({
      ...prev,
      facilities: prev.facilities.includes(facility)
        ? prev.facilities.filter(f => f !== facility)
        : [...prev.facilities, facility]
    }))
  }

  const clearFilters = () => {
    setFilters({
      location: "",
      type: "semua",
      priceRange: [500000, 5000000],
      facilities: [],
      sortBy: "relevance"
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const activeFiltersCount = [
    filters.location && filters.location !== "",
    filters.type !== "semua",
    filters.priceRange[0] !== 500000 || filters.priceRange[1] !== 5000000,
    filters.facilities.length > 0,
    filters.sortBy !== "relevance"
  ].filter(Boolean).length

  return (
    <div className={cn("search-bar", className)}>
      {/* Main Search Input */}
      <div className="flex gap-2 w-full">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="pl-10 pr-4 h-12 text-base"
          />
        </div>
        
        {showFilters && (
          <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="lg" className="h-12 px-4 relative">
                <Filter className="h-4 w-4 mr-2" />
                Filter
                {activeFiltersCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="w-full sm:max-w-md">
              <SheetHeader>
                <SheetTitle className="flex items-center justify-between">
                  <span>Filter Pencarian</span>
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    <X className="h-4 w-4 mr-1" />
                    Reset
                  </Button>
                </SheetTitle>
              </SheetHeader>
              
              <div className="filter-panel mt-6 space-y-6">
                {/* Location Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Lokasi</label>
                  <Select value={filters.location} onValueChange={(value) => 
                    setFilters(prev => ({ ...prev, location: value === "Semua Lokasi" ? "" : value }))
                  }>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih lokasi" />
                    </SelectTrigger>
                    <SelectContent>
                      {locations.map((location) => (
                        <SelectItem key={location} value={location}>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            {location}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Type Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Tipe Kost</label>
                  <Select value={filters.type} onValueChange={(value: any) => 
                    setFilters(prev => ({ ...prev, type: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="semua">Semua Tipe</SelectItem>
                      <SelectItem value="putra">Kost Putra</SelectItem>
                      <SelectItem value="putri">Kost Putri</SelectItem>
                      <SelectItem value="campur">Kost Campur</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Rentang Harga</label>
                  <div className="px-2">
                    <Slider
                      value={filters.priceRange}
                      onValueChange={(value) => 
                        setFilters(prev => ({ ...prev, priceRange: value as [number, number] }))
                      }
                      max={10000000}
                      min={300000}
                      step={100000}
                      className="w-full"
                    />
                  </div>
                  <div className="price-range">
                    <span>{formatPrice(filters.priceRange[0])}</span>
                    <span>{formatPrice(filters.priceRange[1])}</span>
                  </div>
                </div>

                {/* Facilities */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Fasilitas</label>
                  <div className="flex flex-wrap gap-2">
                    {availableFacilities.map((facility) => (
                      <Badge
                        key={facility}
                        variant={filters.facilities.includes(facility) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                        onClick={() => toggleFacility(facility)}
                      >
                        {facility}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Sort By */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Urutkan</label>
                  <Select value={filters.sortBy} onValueChange={(value: any) => 
                    setFilters(prev => ({ ...prev, sortBy: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Paling Relevan</SelectItem>
                      <SelectItem value="price-low">Harga Terendah</SelectItem>
                      <SelectItem value="price-high">Harga Tertinggi</SelectItem>
                      <SelectItem value="rating">Rating Tertinggi</SelectItem>
                      <SelectItem value="newest">Terbaru</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="mt-6 flex gap-2">
                <Button 
                  onClick={() => {
                    handleSearch()
                    setIsFilterOpen(false)
                  }}
                  className="flex-1"
                >
                  Terapkan Filter
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        )}
        
        <Button onClick={handleSearch} size="lg" className="h-12 px-6">
          Cari
        </Button>
      </div>
      
      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-3">
          {filters.location && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {filters.location}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => setFilters(prev => ({ ...prev, location: "" }))}
              />
            </Badge>
          )}
          {filters.type !== "semua" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Kost {filters.type}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => setFilters(prev => ({ ...prev, type: "semua" }))}
              />
            </Badge>
          )}
          {filters.facilities.map((facility) => (
            <Badge key={facility} variant="secondary" className="flex items-center gap-1">
              {facility}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => toggleFacility(facility)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
