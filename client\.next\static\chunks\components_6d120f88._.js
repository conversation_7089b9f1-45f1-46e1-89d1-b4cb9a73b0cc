(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/kost-preview-dialog.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/components_04739116._.js",
  "static/chunks/node_modules__pnpm_2391f0a6._.js",
  "static/chunks/components_kost-preview-dialog_tsx_5ed28599._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/kost-preview-dialog.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/components/comparison-dialog.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_5ff88528._.js",
  "static/chunks/components_comparison-dialog_tsx_5ed28599._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/comparison-dialog.tsx [app-client] (ecmascript)");
    });
});
}),
}]);