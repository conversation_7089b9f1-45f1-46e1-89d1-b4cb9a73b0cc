"use client"

import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"

// Modern Glass Card Component
interface GlassCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  delay?: number
}

export function GlassCard({ children, className, hover = true, delay = 0 }: GlassCardProps) {
  return (
    <div 
      className={cn(
        "glass-morphism rounded-2xl p-6 transition-all duration-500",
        hover && "hover:bg-white/10 hover:scale-105 hover:shadow-2xl",
        className
      )}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  )
}

// Modern Gradient Button
interface GradientButtonProps {
  children: React.ReactNode
  onClick?: () => void
  className?: string
  variant?: "primary" | "secondary" | "outline"
  size?: "sm" | "md" | "lg"
  disabled?: boolean
}

export function GradientButton({ 
  children, 
  onClick, 
  className, 
  variant = "primary",
  size = "md",
  disabled = false 
}: GradientButtonProps) {
  const baseClasses = "font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2"
  
  const variants = {
    primary: "bg-gradient-to-r from-blue-500 to-emerald-500 hover:from-blue-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl",
    secondary: "bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white",
    outline: "border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
  }
  
  const sizes = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
    >
      {children}
    </button>
  )
}

// Modern Icon Container
interface IconContainerProps {
  icon: LucideIcon
  className?: string
  size?: "sm" | "md" | "lg"
  variant?: "gradient" | "glass" | "solid"
}

export function IconContainer({ 
  icon: Icon, 
  className, 
  size = "md",
  variant = "gradient" 
}: IconContainerProps) {
  const sizes = {
    sm: "w-10 h-10",
    md: "w-12 h-12", 
    lg: "w-16 h-16"
  }
  
  const iconSizes = {
    sm: "h-5 w-5",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }
  
  const variants = {
    gradient: "bg-gradient-to-r from-blue-500 to-emerald-500",
    glass: "bg-white/10 backdrop-blur-sm border border-white/20",
    solid: "bg-white/20"
  }

  return (
    <div className={cn(
      "rounded-full flex items-center justify-center transition-all duration-300",
      sizes[size],
      variants[variant],
      className
    )}>
      <Icon className={cn("text-white", iconSizes[size])} />
    </div>
  )
}

// Modern Badge Component
interface ModernBadgeProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "gradient" | "glass"
}

export function ModernBadge({ children, className, variant = "default" }: ModernBadgeProps) {
  const variants = {
    default: "bg-white/20 text-white border-white/30",
    gradient: "bg-gradient-to-r from-blue-500/20 to-emerald-500/20 text-white border-white/20",
    glass: "bg-white/10 backdrop-blur-sm text-white border-white/20"
  }

  return (
    <div className={cn(
      "inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border backdrop-blur-sm",
      variants[variant],
      className
    )}>
      {children}
    </div>
  )
}

// Animated Background Elements
export function AnimatedBackground() {
  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Floating geometric shapes */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-500/20 to-emerald-500/20 rounded-full blur-3xl animate-pulse-glow" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: '2s' }} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-emerald-400/10 rounded-full blur-2xl animate-float" />
      
      {/* Grid pattern overlay */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0z' fill='none'/%3E%3Cpath d='M0 20h40M20 0v40' stroke='%23ffffff' stroke-width='0.5'/%3E%3C/g%3E%3C/svg%3E")`,
      }} />
    </div>
  )
}

// Modern Section Container
interface SectionContainerProps {
  children: React.ReactNode
  className?: string
  background?: "transparent" | "glass" | "gradient"
}

export function SectionContainer({ children, className, background = "transparent" }: SectionContainerProps) {
  const backgrounds = {
    transparent: "",
    glass: "bg-white/5 backdrop-blur-sm border-t border-white/10",
    gradient: "bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border-t border-white/10"
  }

  return (
    <section className={cn("relative", backgrounds[background], className)}>
      {children}
    </section>
  )
}
