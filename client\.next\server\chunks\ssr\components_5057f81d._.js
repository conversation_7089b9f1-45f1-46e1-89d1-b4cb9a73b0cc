module.exports = {

"[project]/components/kost-preview-dialog.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/components_7a9c1d02._.js",
  "server/chunks/ssr/node_modules__pnpm_b06439f2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/kost-preview-dialog.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/components/comparison-dialog.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_c0722ab7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/comparison-dialog.tsx [app-ssr] (ecmascript)");
    });
});
}),

};