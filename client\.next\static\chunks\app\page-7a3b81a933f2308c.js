(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{44:(e,a,s)=>{Promise.resolve().then(s.bind(s,1198))},1198:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>W});var t=s(2273),l=s(5461),i=s(7320),n=s(3427),r=s(1415);function d(e){let{children:a,className:s,hover:l=!0,delay:i=0}=e;return(0,t.jsx)("div",{className:(0,r.cn)("glass-morphism rounded-2xl p-6 transition-all duration-500",l&&"hover:bg-white/10 hover:scale-105 hover:shadow-2xl",s),style:{animationDelay:"".concat(i,"ms")},children:a})}function o(e){let{children:a,onClick:s,className:l,variant:i="primary",size:n="md",disabled:d=!1}=e;return(0,t.jsx)("button",{onClick:s,disabled:d,className:(0,r.cn)("font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2",{primary:"bg-gradient-to-r from-blue-500 to-emerald-500 hover:from-blue-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl",secondary:"bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white",outline:"border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"}[i],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[n],d&&"opacity-50 cursor-not-allowed",l),children:a})}function m(e){let{icon:a,className:s,size:l="md",variant:i="gradient"}=e;return(0,t.jsx)("div",{className:(0,r.cn)("rounded-full flex items-center justify-center transition-all duration-300",{sm:"w-10 h-10",md:"w-12 h-12",lg:"w-16 h-16"}[l],{gradient:"bg-gradient-to-r from-blue-500 to-emerald-500",glass:"bg-white/10 backdrop-blur-sm border border-white/20",solid:"bg-white/20"}[i],s),children:(0,t.jsx)(a,{className:(0,r.cn)("text-white",{sm:"h-5 w-5",md:"h-6 w-6",lg:"h-8 w-8"}[l])})})}function c(e){let{children:a,className:s,variant:l="default"}=e;return(0,t.jsx)("div",{className:(0,r.cn)("inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border backdrop-blur-sm",{default:"bg-white/20 text-white border-white/30",gradient:"bg-gradient-to-r from-blue-500/20 to-emerald-500/20 text-white border-white/20",glass:"bg-white/10 backdrop-blur-sm text-white border-white/20"}[l],s),children:a})}function x(){return(0,t.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-500/20 to-emerald-500/20 rounded-full blur-3xl animate-pulse-glow"}),(0,t.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse-glow",style:{animationDelay:"2s"}}),(0,t.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-emerald-400/10 rounded-full blur-2xl animate-float"}),(0,t.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0z' fill='none'/%3E%3Cpath d='M0 20h40M20 0v40' stroke='%23ffffff' stroke-width='0.5'/%3E%3C/g%3E%3C/svg%3E\")"}})]})}function g(e){let{children:a,className:s,background:l="transparent"}=e;return(0,t.jsx)("section",{className:(0,r.cn)("relative",{transparent:"",glass:"bg-white/5 backdrop-blur-sm border-t border-white/10",gradient:"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border-t border-white/10"}[l],s),children:a})}var h=s(5573),u=s(1101),p=s(1991),b=s(74),f=s(2814),j=s(1640),v=s(3565);let N=(0,s(865).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var k=s(5383);let w=[{icon:h.A,value:"10,000+",label:"Pengguna Aktif"},{icon:u.A,value:"500+",label:"Kost Terdaftar"},{icon:p.A,value:"4.8",label:"Rating Rata-rata"},{icon:b.A,value:"100%",label:"Terverifikasi"}],y=[{icon:f.A,title:"Preview Dinamis",description:"Lihat detail kost dengan preview interaktif dan carousel gambar yang memukau"},{icon:j.A,title:"Perbandingan Mudah",description:"Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan yang detail dan informatif"},{icon:v.A,title:"Terverifikasi",description:"Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan terbaik"}],A=["Jakarta Selatan","Bandung","Yogyakarta","Surabaya","Malang","Semarang"];function C(e){let{onSearch:a}=e;return(0,t.jsxs)("section",{className:"relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:[(0,t.jsx)(x,{}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/20 to-emerald-900/30"}),(0,t.jsx)("div",{className:"relative container mx-auto px-4 py-16 sm:py-20 lg:py-32",children:(0,t.jsxs)("div",{className:"grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[80vh]",children:[(0,t.jsxs)("div",{className:"lg:col-span-7 space-y-6 lg:space-y-8 text-center lg:text-left",children:[(0,t.jsxs)("div",{className:"space-y-4 lg:space-y-6",children:[(0,t.jsxs)(c,{variant:"gradient",className:"inline-flex",children:[(0,t.jsx)(N,{className:"h-4 w-4"}),"Platform Pencarian Kost Terdepan"]}),(0,t.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight",children:["Temukan Kost",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent",children:"Impian Anda"})]}),(0,t.jsx)("p",{className:"text-lg sm:text-xl md:text-2xl text-slate-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed",children:"Platform inovatif dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna."})]}),(0,t.jsx)("div",{className:"max-w-2xl mx-auto lg:mx-0",children:(0,t.jsx)(n.I,{onSearch:a,placeholder:"Cari berdasarkan lokasi, nama kost, atau fasilitas...",className:"bg-white/10 backdrop-blur-md rounded-2xl p-2 border border-white/20 shadow-2xl"})}),(0,t.jsxs)("div",{className:"space-y-3 lg:space-y-4",children:[(0,t.jsx)("p",{className:"text-slate-400 text-sm font-medium",children:"Lokasi Populer:"}),(0,t.jsx)("div",{className:"flex flex-wrap justify-center lg:justify-start gap-2 lg:gap-3",children:A.map((e,s)=>(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300 backdrop-blur-sm text-xs sm:text-sm",onClick:()=>{a("",{location:e,type:"semua",priceRange:[5e5,5e6],facilities:[],sortBy:"relevance"})},style:{animationDelay:"".concat(100*s,"ms")},children:[(0,t.jsx)(u.A,{className:"h-3 w-3 mr-1 sm:mr-2"}),e]},e))})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 lg:gap-4 pt-2 lg:pt-4",children:[(0,t.jsxs)(o,{size:"lg",className:"w-full sm:w-auto",onClick:()=>{var e;null==(e=document.getElementById("kost-listings"))||e.scrollIntoView({behavior:"smooth"})},children:["Jelajahi Kost Sekarang",(0,t.jsx)(k.A,{className:"h-4 w-4 lg:h-5 lg:w-5"})]}),(0,t.jsx)(o,{size:"lg",variant:"outline",className:"w-full sm:w-auto",children:"Pelajari Lebih Lanjut"})]})]}),(0,t.jsx)("div",{className:"lg:col-span-5 mt-8 lg:mt-0",children:(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4 lg:gap-6",children:w.map((e,a)=>(0,t.jsx)(d,{delay:200*a,className:"p-4 lg:p-6",children:(0,t.jsxs)("div",{className:"flex flex-col items-center text-center space-y-2 lg:space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-emerald-400 rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"}),(0,t.jsx)(m,{icon:e.icon,size:"md",variant:"gradient"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl md:text-3xl font-bold text-white",children:e.value}),(0,t.jsx)("div",{className:"text-xs sm:text-sm text-slate-300 font-medium",children:e.label})]})]})},a))})})]})}),(0,t.jsx)(g,{background:"gradient",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-16 sm:py-20",children:[(0,t.jsxs)("div",{className:"text-center mb-12 sm:mb-16",children:[(0,t.jsxs)(c,{variant:"gradient",className:"mb-4 sm:mb-6 inline-flex",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Mengapa Memilih KostHub?"]}),(0,t.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6",children:"Fitur Unggulan Kami"}),(0,t.jsx)("p",{className:"text-slate-300 text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed px-4",children:"Teknologi terdepan yang memudahkan pencarian kost impian Anda dengan pengalaman yang tak terlupakan"})]}),(0,t.jsx)("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 max-w-6xl mx-auto",children:y.map((e,a)=>(0,t.jsx)(d,{delay:200*a,className:"p-6 sm:p-8 text-center sm:text-left",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"relative flex justify-center sm:justify-start",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-emerald-400 rounded-2xl blur-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300"}),(0,t.jsx)(m,{icon:e.icon,size:"lg",variant:"glass",className:"relative"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white",children:e.title}),(0,t.jsx)("p",{className:"text-slate-300 leading-relaxed text-sm sm:text-base",children:e.description})]})]})},a))})]})})]})}var E=s(8919),K=s(9393);function P(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(K.E,{className:"h-8 w-3/4"}),(0,t.jsx)(K.E,{className:"h-4 w-1/2"}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(K.E,{className:"h-4 w-24"}),(0,t.jsx)(K.E,{className:"h-6 w-20"})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(K.E,{className:"h-9 w-20"}),(0,t.jsx)(K.E,{className:"h-9 w-20"})]})]})}),(0,t.jsx)(K.E,{className:"aspect-[16/9] w-full rounded-lg"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:Array.from({length:4}).map((e,a)=>(0,t.jsx)(K.E,{className:"h-9 flex-1"},a))}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(K.E,{className:"h-4 w-full"}),(0,t.jsx)(K.E,{className:"h-4 w-3/4"}),(0,t.jsx)(K.E,{className:"h-4 w-1/2"})]})]})]})}s(298);var M=s(8311),S=s(7466),z=s(2521),Y=s(3293),$=s(6309);let L=(0,l.lazy)(()=>Promise.all([s.e(256),s.e(172)]).then(s.bind(s,7172)).then(e=>({default:e.KostPreviewDialog}))),B=(0,l.lazy)(()=>Promise.all([s.e(450),s.e(914),s.e(352)]).then(s.bind(s,914)).then(e=>({default:e.ComparisonDialog}))),D=[{id:"1",title:"Kost Melati Residence",location:"Kemang, Jakarta Selatan",price:25e5,rating:4.8,reviewCount:124,images:[M.Y$.kost.room1,M.Y$.kost.interior1,M.Y$.kost.interior2],facilities:["WiFi","Parkir","Dapur","Listrik","Air","Keamanan"],type:"putri",available:3,description:"Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",isWishlisted:!1},{id:"2",title:"Griya Mahasiswa Bandung",location:"Dago, Bandung",price:18e5,rating:4.6,reviewCount:89,images:[M.Y$.kost.room2,M.Y$.kost.interior3],facilities:["WiFi","Dapur","Listrik","Air","Ruang Tamu"],type:"putra",available:5,description:"Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",isWishlisted:!0},{id:"3",title:"Kost Harmoni Yogya",location:"Malioboro, Yogyakarta",price:15e5,rating:4.7,reviewCount:156,images:[M.Y$.kost.room3],facilities:["WiFi","Parkir","Listrik","Air","Keamanan","AC"],type:"campur",available:2,description:"Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",isWishlisted:!1}],T=[{name:"Sarah Putri",location:"Jakarta",rating:5,comment:"Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.",avatar:M.Y$.avatars.female1},{name:"Ahmad Rizki",location:"Bandung",rating:5,comment:"Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!",avatar:M.Y$.avatars.male1},{name:"Dina Maharani",location:"Yogyakarta",rating:4,comment:"Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.",avatar:M.Y$.avatars.female2}];function W(){let[e,a]=(0,l.useState)(null),[s,n]=(0,l.useState)(!1),[r,d]=(0,l.useState)([]),[o,m]=(0,l.useState)(!1),[c,x]=(0,l.useState)(["2"]),g=e=>{a(e),n(!0)},f=e=>{x(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},v=e=>{let a=D.find(a=>a.id===e);a&&d(s=>s.some(a=>a.id===e)?s.filter(a=>a.id!==e):s.length<3?[...s,a]:[a,...s.slice(1)])};return(0,t.jsxs)("div",{className:"min-h-screen",children:[(0,t.jsx)(C,{onSearch:(e,a)=>{console.log("Search:",e,a)}}),(0,t.jsx)("section",{id:"kost-listings",className:"py-16 bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsxs)(z.E,{variant:"outline",className:"mb-4",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Kost Terpopuler"]}),(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Kost Pilihan Terbaik"}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Temukan kost berkualitas tinggi yang telah dipilih khusus untuk Anda"})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12",children:D.map(e=>(0,t.jsx)(E.y,{kost:{...e,isWishlisted:c.includes(e.id)},onPreview:g,onWishlist:f,onCompare:v,isComparing:r.some(a=>a.id===e.id)},e.id))}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)(i.$,{size:"lg",variant:"outline",children:["Lihat Semua Kost",(0,t.jsx)(k.A,{className:"h-4 w-4 ml-2"})]})})]})}),(0,t.jsx)(Y.w,{}),(0,t.jsx)("section",{className:"py-16 bg-muted/30",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsxs)(z.E,{variant:"outline",className:"mb-4",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Testimoni Pengguna"]}),(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Apa Kata Mereka?"}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Pengalaman nyata dari pengguna KostHub yang telah menemukan kost impian mereka"})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:T.map((e,a)=>(0,t.jsxs)("div",{className:"bg-card p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"flex items-center gap-1 mb-4",children:Array.from({length:e.rating}).map((e,a)=>(0,t.jsx)(p.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"},a))}),(0,t.jsxs)("p",{className:"text-muted-foreground mb-4 leading-relaxed",children:['"',e.comment,'"']}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"relative w-10 h-10 rounded-full overflow-hidden",children:(0,t.jsx)(S.default,{src:e.avatar,alt:"".concat(e.name," avatar"),fill:!0,className:"object-cover",sizes:"40px"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"h-3 w-3"}),e.location]})]})]})]},a))})]})}),(0,t.jsx)(Y.w,{}),(0,t.jsx)("section",{className:"py-16 bg-background",children:(0,t.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,t.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,t.jsxs)(z.E,{variant:"outline",className:"mb-4",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Bergabung Sekarang"]}),(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold",children:"Siap Menemukan Kost Impian Anda?"}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg",children:"Bergabunglah dengan ribuan pengguna yang telah menemukan tempat tinggal ideal mereka melalui KostHub"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(i.$,{size:"lg",className:"px-8",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Mulai Pencarian"]}),(0,t.jsx)(i.$,{size:"lg",variant:"outline",className:"px-8",children:"Daftarkan Kost Anda"})]})]})})}),r.length>0&&(0,t.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,t.jsxs)(i.$,{onClick:()=>m(!0),className:"rounded-full shadow-lg",size:"lg",children:["Bandingkan (",r.length,")"]})}),(0,t.jsxs)(l.Suspense,{fallback:(0,t.jsx)(P,{}),children:[(0,t.jsx)(L,{kost:e,isOpen:s,onClose:()=>n(!1),onWishlist:f,onCompare:v,isComparing:!!e&&r.some(a=>a.id===e.id)}),(0,t.jsx)(B,{kosts:r,isOpen:o,onClose:()=>m(!1),onRemoveFromComparison:e=>{d(a=>a.filter(a=>a.id!==e))}})]})]})}},1640:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(865).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3565:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(865).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}},e=>{e.O(0,[335,482,739,644,402,214,305,970,804,358],()=>e(e.s=44)),_N_E=e.O()}]);